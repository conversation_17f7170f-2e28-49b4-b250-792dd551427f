using Microsoft.Azure.NotificationHubs;
using Microsoft.Extensions.Options;
using MauiApp10PushNotifications.Models;
using System.Text.Json;

namespace MauiApp10PushNotifications.Services;

/// <summary>
/// Main service for managing push notifications using Azure Notification Hubs
/// </summary>
public class PushNotificationService : IPushNotificationService
{
    private readonly IDeviceInstallationService deviceInstallationService;
    private readonly INotificationRegistrationService registrationService;
    private readonly INotificationActionService actionService;
    private readonly ILogger<PushNotificationService> logger;
    private readonly NotificationHubClient hubClient;
    private readonly PushNotificationErrorHandler errorHandler;

    private NotificationRegistrationStatus registrationStatus;
    private bool isInitialized;

    public PushNotificationService(
        IDeviceInstallationService deviceInstallationService,
        INotificationRegistrationService registrationService,
        INotificationActionService actionService,
        IOptions<NotificationHubOptions> options,
        ILogger<PushNotificationService> logger,
        PushNotificationErrorHandler errorHandler)
    {
        this.deviceInstallationService = deviceInstallationService;
        this.registrationService = registrationService;
        this.actionService = actionService;
        this.logger = logger;
        this.errorHandler = errorHandler;

        this.hubClient = NotificationHubClient.CreateClientFromConnectionString(
            options.Value.ConnectionString,
            options.Value.Name);

        this.registrationStatus = new NotificationRegistrationStatus();

        // Subscribe to action service events
        this.actionService.ActionTriggered += OnActionTriggered;
    }

    /// <summary>
    /// Event fired when a push notification is received
    /// </summary>
    public event EventHandler<PushNotification>? NotificationReceived;

    /// <summary>
    /// Event fired when the registration status changes
    /// </summary>
    public event EventHandler<NotificationRegistrationStatus>? RegistrationStatusChanged;

    /// <summary>
    /// Gets the current registration status
    /// </summary>
    public NotificationRegistrationStatus RegistrationStatus => this.registrationStatus;

    /// <summary>
    /// Checks if push notifications are supported on this device
    /// </summary>
    public bool IsSupported => this.deviceInstallationService.NotificationsSupported;

    /// <summary>
    /// Initializes the push notification service
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            if (this.isInitialized)
            {
                this.logger.LogInformation("Push notification service already initialized");
                return;
            }

            if (!IsSupported)
            {
                this.logger.LogWarning("Push notifications are not supported on this device");
                UpdateRegistrationStatus(false, null, "Push notifications not supported on this device");
                return;
            }

            this.logger.LogInformation("Initializing push notification service");
            this.isInitialized = true;

            // Initial registration status
            UpdateRegistrationStatus(false, this.deviceInstallationService.Token, null);
        }
        catch (Exception ex)
        {
            this.errorHandler.HandlePushNotificationError(ex, "initialization", false);
            UpdateRegistrationStatus(false, null, ex.Message);
        }
    }

    /// <summary>
    /// Registers the device for push notifications
    /// </summary>
    public async Task<bool> RegisterAsync(params string[] tags)
    {
        try
        {
            if (!IsSupported)
            {
                this.logger.LogWarning("Cannot register: Push notifications not supported");
                return false;
            }

            this.logger.LogInformation("Registering device for push notifications with tags: {Tags}",
                string.Join(", ", tags));

            var success = await this.registrationService.RefreshRegistrationAsync();

            if (success)
            {
                UpdateRegistrationStatus(true, this.deviceInstallationService.Token, null);
                this.errorHandler.LogSuccess("registration", $"Tags: {string.Join(", ", tags)}");
            }
            else
            {
                UpdateRegistrationStatus(false, this.deviceInstallationService.Token, "Registration failed");
                this.errorHandler.LogWarning("registration", "Registration failed without exception");
            }

            return success;
        }
        catch (Exception ex)
        {
            this.errorHandler.HandleNotificationHubError(ex, "registration", false);
            UpdateRegistrationStatus(false, this.deviceInstallationService.Token, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Unregisters the device from push notifications
    /// </summary>
    public async Task<bool> UnregisterAsync()
    {
        try
        {
            this.logger.LogInformation("Unregistering device from push notifications");

            var success = await this.registrationService.DeregisterDeviceAsync();

            if (success)
            {
                UpdateRegistrationStatus(false, null, null);
                this.errorHandler.LogSuccess("unregistration");
            }
            else
            {
                this.errorHandler.LogWarning("unregistration", "Unregistration failed without exception");
            }

            return success;
        }
        catch (Exception ex)
        {
            this.errorHandler.HandleNotificationHubError(ex, "unregistration", false);
            return false;
        }
    }

    /// <summary>
    /// Sends a test notification (for testing purposes)
    /// </summary>
    public async Task<bool> SendTestNotificationAsync(NotificationRequest request)
    {
        try
        {
            this.logger.LogInformation("Sending test notification: {Title}", request.Text);

            var androidPayload = PrepareNotificationPayload(
                request.Silent ? PushTemplates.Silent.Android : PushTemplates.Generic.Android,
                "Test Notification",
                request.Text,
                request.Action);

            // Send to all registered devices (broadcast)
            await this.hubClient.SendFcmV1NativeNotificationAsync(androidPayload);

            this.errorHandler.LogSuccess("sending", $"Test notification: {request.Text}");
            return true;
        }
        catch (Exception ex)
        {
            this.errorHandler.HandleNotificationHubError(ex, "sending", false);
            return false;
        }
    }

    /// <summary>
    /// Gets the device installation information
    /// </summary>
    public DeviceInstallation? GetDeviceInstallation(params string[] tags)
    {
        try
        {
            return this.deviceInstallationService.GetDeviceInstallation(tags);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error getting device installation");
            return null;
        }
    }

    /// <summary>
    /// Handles received push notifications
    /// </summary>
    public void HandleReceivedNotification(PushNotification notification)
    {
        try
        {
            this.logger.LogInformation("Received push notification: {Title}", notification.Title);
            NotificationReceived?.Invoke(this, notification);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error handling received notification");
        }
    }

    private void OnActionTriggered(object? sender, string action)
    {
        this.logger.LogInformation("Notification action triggered: {Action}", action);

        // Handle specific actions here
        switch (action.ToLowerInvariant())
        {
            case "open_app":
                // App is already open when this is called
                break;
            case "view_tasks":
                // Navigate to tasks page
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Shell.Current.GoToAsync("//main");
                });
                break;
            case "view_projects":
                // Navigate to projects page
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Shell.Current.GoToAsync("//projects");
                });
                break;
            default:
                this.logger.LogInformation("Unknown action: {Action}", action);
                break;
        }
    }

    private void UpdateRegistrationStatus(bool isRegistered, string? token, string? error)
    {
        this.registrationStatus = new NotificationRegistrationStatus
        {
            IsRegistered = isRegistered,
            Token = token,
            Error = error,
            LastUpdated = DateTime.Now
        };

        RegistrationStatusChanged?.Invoke(this, this.registrationStatus);
    }

    private static string PrepareNotificationPayload(string template, string title, string body, string action)
    {
        return template
            .Replace("$(title)", title)
            .Replace("$(body)", body)
            .Replace("$(action)", action);
    }
}
