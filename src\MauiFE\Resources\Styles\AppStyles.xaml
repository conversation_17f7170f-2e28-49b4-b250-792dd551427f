﻿<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:f="clr-namespace:Fonts"
        xmlns:pullToRefresh="clr-namespace:Syncfusion.Maui.Toolkit.PullToRefresh;assembly=Syncfusion.Maui.Toolkit"
        xmlns:sf="clr-namespace:Syncfusion.Maui.Toolkit.TextInputLayout;assembly=Syncfusion.Maui.Toolkit"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

        <x:Double x:Key="sizeNone">0</x:Double>
        <x:Double x:Key="size20">2</x:Double>
        <x:Double x:Key="size40">4</x:Double>
        <x:Double x:Key="size60">6</x:Double>
        <x:Double x:Key="size80">8</x:Double>
        <x:Double x:Key="size100">10</x:Double>
        <x:Double x:Key="size120">12</x:Double>
        <x:Double x:Key="size160">16</x:Double>
        <x:Double x:Key="size200">20</x:Double>
        <x:Double x:Key="size240">24</x:Double>
        <x:Double x:Key="size280">28</x:Double>
        <x:Double x:Key="size320">32</x:Double>
        <x:Double x:Key="size360">36</x:Double>
        <x:Double x:Key="size400">40</x:Double>
        <x:Double x:Key="size480">48</x:Double>
        <x:Double x:Key="size520">52</x:Double>
        <x:Double x:Key="size560">56</x:Double>

        <!-- https://fluent2.microsoft.design/typography/ -->
        <OnIdiom x:Key="IconSize"
                 x:TypeArguments="x:Double"
                 Default="20">
                <OnIdiom.Desktop>32</OnIdiom.Desktop>
        </OnIdiom>

        <OnIdiom x:Key="IconSizeSmall"
                 x:TypeArguments="x:Double"
                 Default="12">
                <OnIdiom.Desktop>18</OnIdiom.Desktop>
        </OnIdiom>

        <OnIdiom x:Key="LayoutPadding"
                 x:TypeArguments="Thickness"
                 Default="15">
                <OnIdiom.Desktop>30</OnIdiom.Desktop>
        </OnIdiom>

        <OnIdiom x:Key="LayoutSpacing"
                 x:TypeArguments="x:Double"
                 Default="5">
                <OnIdiom.Desktop>15</OnIdiom.Desktop>
        </OnIdiom>

        <FontImageSource x:Key="IconDashboard"
                         Glyph="{x:Static f:FluentUI.diagram_24_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconProjects"
                         Glyph="{x:Static f:FluentUI.list_24_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconMeta"
                         Glyph="{x:Static f:FluentUI.info_24_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconNotifications"
                         Glyph="{x:Static f:FluentUI.alert_24_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconRibbon"
                         Glyph="{x:Static f:FluentUI.ribbon_20_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconAdd"
                         Glyph="{x:Static f:FluentUI.add_32_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconDelete"
                         Glyph="{x:Static f:FluentUI.delete_32_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource IconSize}"/>

        <FontImageSource x:Key="IconClean"
                         Glyph="{x:Static f:FluentUI.broom_32_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource size240}"/>

        <FontImageSource x:Key="IconLight"
                         Glyph="{x:Static f:FluentUI.weather_sunny_28_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource size240}"/>
        <FontImageSource x:Key="IconDark"
                         Glyph="{x:Static f:FluentUI.weather_moon_28_regular}"
                         FontFamily="{x:Static f:FluentUI.FontFamily}"
                         Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                         Size="{StaticResource size240}"/>

        <Style TargetType="Label">
                <Setter Property="TextColor"
                        Value="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"/>
                <Setter Property="BackgroundColor"
                        Value="Transparent"/>
                <Setter Property="FontSize"
                        Value="17"/>
                <Setter Property="LineHeight"
                        Value="1.29"/>
                <!-- 22 -->
                <Setter Property="VisualStateManager.VisualStateGroups">
                        <VisualStateGroupList>
                                <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="Disabled">
                                                <VisualState.Setters>
                                                        <Setter Property="TextColor"
                                                                Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"/>
                                                </VisualState.Setters>
                                        </VisualState>
                                </VisualStateGroup>
                        </VisualStateGroupList>
                </Setter>
        </Style>
        <Style x:Key="Caption2"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="12"/>
                <Setter Property="LineHeight"
                        Value="1.33"/>
                <!-- 16 -->
        </Style>
        <Style x:Key="Caption1"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="13"/>
                <Setter Property="LineHeight"
                        Value="1.38"/>
                <!-- 18 -->
        </Style>
        <Style x:Key="Caption1Strong"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="13"/>
                <Setter Property="LineHeight"
                        Value="1.38"/>
                <!-- 18 -->
                <Setter Property="FontFamily"
                        Value="{OnPlatform WinUI=SegoeSemibold, iOS=.SFUI-SemiBold, MacCatalyst=.SFUI-SemiBold, Default=''}"/>
                <Setter Property="FontAttributes"
                        Value="{OnPlatform Default=None, Android=Bold}"/>
        </Style>
        <Style x:Key="Body2"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="15"/>
                <Setter Property="LineHeight"
                        Value="1.33"/>
                <!-- 20 -->
        </Style>
        <Style x:Key="Body2Strong"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="15"/>
                <Setter Property="LineHeight"
                        Value="1.33"/>
                <!-- 20 -->
                <Setter Property="FontFamily"
                        Value="{OnPlatform WinUI=SegoeSemibold, iOS=.SFUI-SemiBold, MacCatalyst=.SFUI-SemiBold, Default=''}"/>
                <Setter Property="FontAttributes"
                        Value="{OnPlatform Default=None, Android=Bold}"/>
        </Style>
        <Style x:Key="Body1"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="17"/>
                <Setter Property="LineHeight"
                        Value="1.29"/>
                <!-- 22 -->
        </Style>
        <Style x:Key="Body1Strong"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="17"/>
                <Setter Property="LineHeight"
                        Value="1.29"/>
                <!-- 22 -->
                <Setter Property="FontFamily"
                        Value="{OnPlatform WinUI=SegoeSemibold, iOS=.SFUI-SemiBold, MacCatalyst=.SFUI-SemiBold, Default=''}"/>
                <Setter Property="FontAttributes"
                        Value="{OnPlatform Default=None, Android=Bold}"/>
        </Style>
        <Style x:Key="Title3"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="20"/>
                <Setter Property="LineHeight"
                        Value="1.25"/>
                <!-- 25 -->
                <Setter Property="FontFamily"
                        Value="{OnPlatform WinUI=SegoeSemibold, iOS=.SFUI-SemiBold, MacCatalyst=.SFUI-SemiBold, Default=''}"/>
                <Setter Property="FontAttributes"
                        Value="{OnPlatform Default=None, Android=Bold}"/>
        </Style>
        <Style x:Key="Title2"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="22"/>
                <Setter Property="LineHeight"
                        Value="1.27"/>
                <!-- 28 -->
                <Setter Property="FontFamily"
                        Value="{OnPlatform WinUI=SegoeSemibold, iOS=.SFUI-SemiBold, MacCatalyst=.SFUI-SemiBold, Default=''}"/>
                <Setter Property="FontAttributes"
                        Value="{OnPlatform Android=Bold, Default=None}"/>
        </Style>
        <Style x:Key="Title1"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="28"/>
                <Setter Property="LineHeight"
                        Value="1.21"/>
                <!-- 34 -->
                <Setter Property="FontAttributes"
                        Value="{OnPlatform WinUI=None, Default=Bold}"/>
        </Style>
        <Style x:Key="LargeTitle"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="34"/>
                <Setter Property="LineHeight"
                        Value="1.21"/>
                <!-- 41 -->
                <Setter Property="FontAttributes"
                        Value="{OnPlatform WinUI=None, Default=Bold}"/>
        </Style>
        <Style x:Key="Display"
               TargetType="Label">
                <Setter Property="FontSize"
                        Value="60"/>
                <Setter Property="LineHeight"
                        Value="1.17"/>
                <!-- 70 -->
                <Setter Property="FontAttributes"
                        Value="{OnPlatform WinUI=None, Default=Bold}"/>
        </Style>

        <Style TargetType="Border">
                <Setter Property="StrokeShape"
                                Value="RoundRectangle 20"/>
                <Setter Property="Background"
                                Value="{AppThemeBinding Light={StaticResource LightSecondaryBackground},Dark={StaticResource DarkSecondaryBackground}}"/>
                <Setter Property="StrokeThickness"
                                Value="0"/>
                <Setter Property="Padding"
                                Value="{OnIdiom 15, Desktop=20}"/>
        </Style>

        <Style TargetType="Border"
                        x:Key="CardStyle">
                <Setter Property="StrokeShape"
                                Value="RoundRectangle 20"/>
                <Setter Property="Background"
                                Value="{AppThemeBinding Light={StaticResource LightSecondaryBackground},Dark={StaticResource DarkSecondaryBackground}}"/>
                <Setter Property="StrokeThickness"
                                Value="0"/>
                <Setter Property="Padding"
                                Value="{OnIdiom 15, Desktop=20}"/>
        </Style>

        <sf:LabelStyle x:Key="lightThemeLabelStyle"
                        TextColor="{StaticResource DarkOnLightBackground}"
                        FontSize="{OnIdiom 18, Desktop=24}"/>
        <sf:LabelStyle x:Key="darkThemeLabelStyle"
                        TextColor="{StaticResource LightOnDarkBackground}"
                        FontSize="{OnIdiom 18, Desktop=24}"/>
        <Style TargetType="sf:SfTextInputLayout">
                <Setter Property="ContainerType"
                                Value="Outlined"/>
                <Setter Property="ContainerBackground"
                                Value="Transparent"/>
                <Setter Property="Stroke"
                                Value="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"/>
                <Setter Property="HintLabelStyle"
                                Value="{AppThemeBinding Light={StaticResource lightThemeLabelStyle}, Dark={StaticResource darkThemeLabelStyle}}"/>
        </Style>


        <Style TargetType="BoxView"
                        x:Key="ShimmerCustomViewStyle">
                <Setter Property="BackgroundColor"
                                Value="Gray"/>
                <Setter Property="HorizontalOptions"
                                Value="FillAndExpand"/>
                <Setter Property="VerticalOptions"
                                Value="CenterAndExpand"/>
        </Style>

        <Style TargetType="pullToRefresh:SfPullToRefresh">
                <Setter Property="TransitionMode"
                                Value="SlideOnTop"/>
                <Setter Property="PullingThreshold"
                                Value="100"/>
                <Setter Property="ProgressColor"
                                Value="{StaticResource Primary}"/>
        </Style>

</ResourceDictionary>