using MauiApp10PushNotifications.PageModels;

namespace MauiApp10PushNotifications.Pages;

public partial class PushNotificationPage : ContentPage
{
    public PushNotificationPage(PushNotificationPageModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        // Auto-initialize when page appears
        if (BindingContext is PushNotificationPageModel viewModel)
        {
            await viewModel.InitializeCommand.ExecuteAsync(null);
        }
    }
}
