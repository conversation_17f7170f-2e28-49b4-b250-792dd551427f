using Microsoft.Azure.NotificationHubs;

namespace MauiApp10PushNotifications.Services;

/// <summary>
/// Specialized error handler for push notification operations
/// </summary>
public class PushNotificationErrorHandler
{
    private readonly ILogger<PushNotificationErrorHandler> logger;
    private readonly IErrorHandler errorHandler;

    public PushNotificationErrorHandler(
        ILogger<PushNotificationErrorHandler> logger,
        IErrorHandler errorHandler)
    {
        this.logger = logger;
        this.errorHandler = errorHandler;
    }

    /// <summary>
    /// Handles push notification specific errors with detailed logging and user-friendly messages
    /// </summary>
    public void HandlePushNotificationError(Exception ex, string operation, bool showToUser = true)
    {
        var userMessage = GetUserFriendlyMessage(ex, operation);
        var logMessage = $"Push notification error during {operation}: {ex.Message}";

        // Log the detailed error
        this.logger.LogError(ex, logMessage);

        // Show user-friendly error if requested
        if (showToUser)
        {
            var userException = new Exception(userMessage, ex);
            this.errorHandler.HandleError(userException);
        }
    }

    /// <summary>
    /// Handles Azure Notification Hub specific errors
    /// </summary>
    public void HandleNotificationHubError(Exception ex, string operation, bool showToUser = true)
    {
        string userMessage;
        string logMessage = $"Azure Notification Hub error during {operation}: {ex.Message}";

        // Handle specific Azure Notification Hub exceptions
        switch (ex)
        {
            case Microsoft.Azure.NotificationHubs.Messaging.MessagingException nhEx:
                userMessage = GetNotificationHubErrorMessage(nhEx, operation);
                this.logger.LogError(ex, "MessagingException during {Operation}: {Detail} - {Message}",
                    operation, nhEx.Detail, nhEx.Message);
                break;

            case UnauthorizedAccessException:
                userMessage = "Authentication failed. Please check your notification hub configuration.";
                this.logger.LogError(ex, "Unauthorized access during {Operation}", operation);
                break;

            case TimeoutException:
                userMessage = "The operation timed out. Please check your network connection and try again.";
                this.logger.LogError(ex, "Timeout during {Operation}", operation);
                break;

            case HttpRequestException httpEx:
                userMessage = "Network error occurred. Please check your internet connection.";
                this.logger.LogError(ex, "HTTP error during {Operation}: {StatusCode}", operation, httpEx.Data["StatusCode"]);
                break;

            default:
                userMessage = GetUserFriendlyMessage(ex, operation);
                this.logger.LogError(ex, logMessage);
                break;
        }

        // Show user-friendly error if requested
        if (showToUser)
        {
            var userException = new Exception(userMessage, ex);
            this.errorHandler.HandleError(userException);
        }
    }

    /// <summary>
    /// Handles Firebase/FCM specific errors
    /// </summary>
    public void HandleFirebaseError(Exception ex, string operation, bool showToUser = true)
    {
        var userMessage = ex.Message switch
        {
            var msg when msg.Contains("INVALID_REGISTRATION") => "Device registration is invalid. Please re-register for notifications.",
            var msg when msg.Contains("NOT_REGISTERED") => "Device is not registered for notifications. Please register first.",
            var msg when msg.Contains("INVALID_PACKAGE_NAME") => "App configuration error. Please contact support.",
            var msg when msg.Contains("MISMATCH_SENDER_ID") => "Firebase configuration mismatch. Please check app setup.",
            var msg when msg.Contains("MESSAGE_TOO_BIG") => "Notification message is too large. Please reduce the content.",
            var msg when msg.Contains("INVALID_DATA_KEY") => "Invalid notification data format.",
            var msg when msg.Contains("INVALID_TTL") => "Invalid notification time-to-live setting.",
            var msg when msg.Contains("UNAVAILABLE") => "Firebase service is temporarily unavailable. Please try again later.",
            var msg when msg.Contains("INTERNAL_SERVER_ERROR") => "Firebase internal error. Please try again later.",
            _ => $"Firebase error during {operation}. Please try again."
        };

        this.logger.LogError(ex, "Firebase error during {Operation}: {Message}", operation, ex.Message);

        if (showToUser)
        {
            var userException = new Exception(userMessage, ex);
            this.errorHandler.HandleError(userException);
        }
    }

    /// <summary>
    /// Logs successful operations for monitoring
    /// </summary>
    public void LogSuccess(string operation, string details = "")
    {
        this.logger.LogInformation("Push notification operation succeeded: {Operation} {Details}", operation, details);
    }

    /// <summary>
    /// Logs warnings for non-critical issues
    /// </summary>
    public void LogWarning(string operation, string message, Exception? ex = null)
    {
        if (ex != null)
        {
            this.logger.LogWarning(ex, "Push notification warning during {Operation}: {Message}", operation, message);
        }
        else
        {
            this.logger.LogWarning("Push notification warning during {Operation}: {Message}", operation, message);
        }
    }

    private string GetUserFriendlyMessage(Exception ex, string operation)
    {
        return operation.ToLowerInvariant() switch
        {
            "initialization" => "Failed to initialize push notifications. Please check your device settings.",
            "registration" => "Failed to register for push notifications. Please check your network connection and try again.",
            "unregistration" => "Failed to unregister from push notifications. Please try again.",
            "sending" => "Failed to send notification. Please check your configuration and try again.",
            "token_refresh" => "Failed to refresh notification token. Please restart the app.",
            _ => $"An error occurred during {operation}. Please try again."
        };
    }

    private string GetNotificationHubErrorMessage(Microsoft.Azure.NotificationHubs.Messaging.MessagingException ex, string operation)
    {
        // MessagingException uses Detail property instead of ErrorCode
        var detail = ex.Detail?.ToString() ?? ex.Message;

        return detail switch
        {
            var d when d.Contains("Unauthorized") => "Authentication failed. Please check your notification hub credentials.",
            var d when d.Contains("Forbidden") => "Access denied. Please check your notification hub permissions.",
            var d when d.Contains("NotFound") => "Notification hub not found. Please check your configuration.",
            var d when d.Contains("Conflict") => "A conflict occurred. Please try again.",
            var d when d.Contains("TooManyRequests") => "Too many requests. Please wait a moment and try again.",
            var d when d.Contains("InternalServerError") => "Azure service error. Please try again later.",
            var d when d.Contains("BadGateway") => "Gateway error. Please check your network connection.",
            var d when d.Contains("ServiceUnavailable") => "Azure Notification Hubs service is temporarily unavailable.",
            var d when d.Contains("GatewayTimeout") => "Request timed out. Please try again.",
            _ => $"Azure Notification Hub error during {operation}. Please try again."
        };
    }
}
