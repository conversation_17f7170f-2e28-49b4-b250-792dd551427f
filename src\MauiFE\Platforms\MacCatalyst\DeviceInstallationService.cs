using Foundation;
using MauiApp10PushNotifications.Models;
using MauiApp10PushNotifications.Services;
using UIKit;
using UserNotifications;

namespace MauiApp10PushNotifications.Platforms.MacCatalyst;

/// <summary>
/// MacCatalyst-specific device installation service
/// Uses APNS like iOS but with MacCatalyst-specific considerations
/// </summary>
public class DeviceInstallationService : IDeviceInstallationService
{
    private const int SupportedVersionMajor = 13;
    private const int SupportedVersionMinor = 0;

    /// <summary>
    /// Gets or sets the push notification token
    /// </summary>
    public string? Token { get; set; }

    /// <summary>
    /// Checks if notifications are supported on this device
    /// MacCatalyst supports notifications through APNS
    /// </summary>
    public bool NotificationsSupported =>
        UIDevice.CurrentDevice.CheckSystemVersion(SupportedVersionMajor, SupportedVersionMinor);

    /// <summary>
    /// Gets a unique device identifier for MacCatalyst
    /// </summary>
    public string GetDeviceId()
    {
        return UIDevice.CurrentDevice.IdentifierForVendor?.ToString() ?? Guid.NewGuid().ToString();
    }

    /// <summary>
    /// Gets the device installation information for MacCatalyst
    /// </summary>
    public DeviceInstallation GetDeviceInstallation(params string[] tags)
    {
        if (!NotificationsSupported)
        {
            throw new Exception(GetNotificationsSupportError());
        }

        if (string.IsNullOrWhiteSpace(Token))
        {
            throw new Exception("Unable to resolve token for APNS on MacCatalyst");
        }

        var installation = new DeviceInstallation
        {
            InstallationId = GetDeviceId(),
            Platform = "apns", // MacCatalyst uses APNS like iOS
            PushChannel = Token
        };

        if (tags?.Length > 0)
        {
            foreach (var tag in tags)
            {
                installation.Tags.Add(tag);
            }
        }

        return installation;
    }

    private string GetNotificationsSupportError()
    {
        if (!NotificationsSupported)
        {
            return $"This app only supports notifications on macOS {SupportedVersionMajor}.{SupportedVersionMinor} and above. You are running {UIDevice.CurrentDevice.SystemVersion}.";
        }

        if (string.IsNullOrWhiteSpace(Token))
        {
            return "This app can support notifications but you must enable this in your settings.";
        }

        return "An error occurred preventing the use of push notifications";
    }
}

/// <summary>
/// Extension methods for NSData (shared with iOS implementation)
/// </summary>
internal static class NSDataExtensions
{
    internal static string ToHexString(this NSData data)
    {
        var bytes = data.ToArray();

        if (bytes == null)
            return string.Empty;

        var sb = new System.Text.StringBuilder(bytes.Length * 2);

        foreach (byte b in bytes)
            sb.AppendFormat("{0:x2}", b);

        return sb.ToString().ToUpperInvariant();
    }
}
