namespace MauiApp10PushNotifications.Services;

/// <summary>
/// Service for handling notification actions
/// </summary>
public class NotificationActionService : INotificationActionService
{
    private readonly ILogger<NotificationActionService> logger;

    public NotificationActionService(ILogger<NotificationActionService> logger)
    {
        this.logger = logger;
    }

    /// <summary>
    /// Event fired when a notification action is triggered
    /// </summary>
    public event EventHandler<string>? ActionTriggered;

    /// <summary>
    /// Triggers a notification action
    /// </summary>
    /// <param name="action">The action to trigger</param>
    public void TriggerAction(string action)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(action))
            {
                this.logger.LogWarning("Attempted to trigger empty or null action");
                return;
            }

            this.logger.LogInformation("Triggering notification action: {Action}", action);
            ActionTriggered?.Invoke(this, action);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error triggering notification action: {Action}", action);
        }
    }
}
