using Microsoft.Azure.NotificationHubs;
using Microsoft.Extensions.Options;
using MauiApp10PushNotifications.Models;
using System.Text.Json;

namespace MauiApp10PushNotifications.Services;

/// <summary>
/// Service for managing notification hub registration
/// </summary>
public class NotificationRegistrationService : INotificationRegistrationService
{
    private readonly NotificationHubClient hubClient;
    private readonly IDeviceInstallationService deviceInstallationService;
    private readonly ILogger<NotificationRegistrationService> logger;
    private readonly Dictionary<string, NotificationPlatform> installationPlatform;

    public NotificationRegistrationService(
        IOptions<NotificationHubOptions> options,
        IDeviceInstallationService deviceInstallationService,
        ILogger<NotificationRegistrationService> logger)
    {
        this.deviceInstallationService = deviceInstallationService;
        this.logger = logger;

        this.hubClient = NotificationHubClient.CreateClientFromConnectionString(
            options.Value.ConnectionString,
            options.Value.Name);

        this.installationPlatform = new Dictionary<string, NotificationPlatform>
        {
            { nameof(NotificationPlatform.Apns).ToLower(), NotificationPlatform.Apns },
            { nameof(NotificationPlatform.FcmV1).ToLower(), NotificationPlatform.FcmV1 }
        };
    }

    /// <summary>
    /// Refreshes the device registration with the notification hub
    /// </summary>
    public async Task<bool> RefreshRegistrationAsync()
    {
        try
        {
            var deviceInstallation = this.deviceInstallationService.GetDeviceInstallation();

            if (deviceInstallation == null)
            {
                this.logger.LogWarning("Device installation is null, cannot refresh registration");
                return false;
            }

            return await CreateOrUpdateInstallationAsync(deviceInstallation);
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error refreshing device registration");
            return false;
        }
    }

    /// <summary>
    /// Deregisters the device from the notification hub
    /// </summary>
    public async Task<bool> DeregisterDeviceAsync()
    {
        try
        {
            var deviceId = this.deviceInstallationService.GetDeviceId();

            if (string.IsNullOrWhiteSpace(deviceId))
            {
                this.logger.LogWarning("Device ID is null or empty, cannot deregister");
                return false;
            }

            await this.hubClient.DeleteInstallationAsync(deviceId);
            this.logger.LogInformation("Successfully deregistered device: {DeviceId}", deviceId);
            return true;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error deregistering device");
            return false;
        }
    }

    private async Task<bool> CreateOrUpdateInstallationAsync(DeviceInstallation deviceInstallation)
    {
        if (string.IsNullOrWhiteSpace(deviceInstallation?.InstallationId) ||
            string.IsNullOrWhiteSpace(deviceInstallation?.Platform) ||
            string.IsNullOrWhiteSpace(deviceInstallation?.PushChannel))
        {
            this.logger.LogWarning("Invalid device installation data");
            return false;
        }

        var installation = new Installation()
        {
            InstallationId = deviceInstallation.InstallationId,
            PushChannel = deviceInstallation.PushChannel,
            Tags = deviceInstallation.Tags
        };

        if (this.installationPlatform.TryGetValue(deviceInstallation.Platform, out var platform))
        {
            installation.Platform = platform;
        }
        else
        {
            this.logger.LogWarning("Unsupported platform: {Platform}", deviceInstallation.Platform);
            return false;
        }

        try
        {
            await this.hubClient.CreateOrUpdateInstallationAsync(installation);
            this.logger.LogInformation("Successfully registered/updated device installation: {InstallationId}",
                deviceInstallation.InstallationId);
            return true;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error creating or updating installation: {InstallationId}",
                deviceInstallation.InstallationId);
            return false;
        }
    }
}
