<?xml version="1.0" encoding="utf-8" ?>
<Border xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        xmlns:controls="clr-namespace:MauiApp10PushNotifications.Pages.Controls"
        xmlns:models="clr-namespace:MauiApp10PushNotifications.Models"
        xmlns:fonts="clr-namespace:Fonts"
        xmlns:pageModels="clr-namespace:MauiApp10PushNotifications.PageModels"
        xmlns:shimmer="clr-namespace:Syncfusion.Maui.Toolkit.Shimmer;assembly=Syncfusion.Maui.Toolkit"
        x:Class="MauiApp10PushNotifications.Pages.Controls.ProjectCardView"
        Style="{StaticResource CardStyle}"
        x:DataType="models:Project">
    <shimmer:SfShimmer
        BackgroundColor="Transparent"
        VerticalOptions="FillAndExpand"
        IsActive="{Binding IsBusy, Source={RelativeSource AncestorType={x:Type pageModels:MainPageModel}}, x:DataType=pageModels:IProjectTaskPageModel}">
        <shimmer:SfShimmer.CustomView>
            <VerticalStackLayout Spacing="15">
                <BoxView 
                    CornerRadius="48"
                    WidthRequest="24"
                    HeightRequest="24"
                    HorizontalOptions="Start"
                    VerticalOptions="Start"
                    Style="{StaticResource ShimmerCustomViewStyle}"/>
                <BoxView 
                    HeightRequest="24"
                    Style="{StaticResource ShimmerCustomViewStyle}"/>
                <BoxView 
                    HeightRequest="48"
                    Style="{StaticResource ShimmerCustomViewStyle}"/>
                <BoxView 
                    HeightRequest="24"
                    Margin="0, 12"
                    Style="{StaticResource ShimmerCustomViewStyle}"/>
            </VerticalStackLayout>
        </shimmer:SfShimmer.CustomView>
        <shimmer:SfShimmer.Content>
            <VerticalStackLayout Spacing="15">
                <Image HorizontalOptions="Start" Aspect="Center">
                    <Image.Source>
                        <FontImageSource 
                    Glyph="{Binding Icon}" 
                    FontFamily="{x:Static fonts:FluentUI.FontFamily}" 
                    Color="{AppThemeBinding Light={StaticResource DarkOnLightBackground}, Dark={StaticResource LightOnDarkBackground}}"
                    Size="{StaticResource IconSize}"/>
                    </Image.Source>
                </Image>
                <Label Text="{Binding Name}" TextColor="{StaticResource Gray400}" FontSize="14" TextTransform="Uppercase"/>
                <Label Text="{Binding Description}" LineBreakMode="WordWrap"/>
                <HorizontalStackLayout Spacing="15" BindableLayout.ItemsSource="{Binding Tags}">
                    <BindableLayout.ItemTemplate>
                        <DataTemplate x:DataType="models:Tag">
                            <controls:TagView />
                        </DataTemplate>
                    </BindableLayout.ItemTemplate>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </shimmer:SfShimmer.Content>
    </shimmer:SfShimmer>
</Border>