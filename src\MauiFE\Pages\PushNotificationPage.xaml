<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pageModels="clr-namespace:MauiApp10PushNotifications.PageModels"
             xmlns:models="clr-namespace:MauiApp10PushNotifications.Models"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="MauiApp10PushNotifications.Pages.PushNotificationPage"
             x:DataType="pageModels:PushNotificationPageModel"
             Title="Push Notifications">
    <ScrollView>
        <StackLayout Padding="20"
                Spacing="15">

            <!-- Status Section -->
            <Frame BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                   BorderColor="Transparent"
                   CornerRadius="10"
                   Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="Status"
                           FontSize="18"
                           FontAttributes="Bold"/>

                    <Grid ColumnDefinitions="Auto,*"
                            RowDefinitions="Auto,Auto,Auto,Auto"
                            ColumnSpacing="10"
                            RowSpacing="5">
                        <Label Grid.Row="0"
                                Grid.Column="0"
                                Text="Supported:"
                                FontAttributes="Bold"/>
                        <Label Grid.Row="0"
                                Grid.Column="1"
                                Text="{Binding IsSupported}"/>

                        <Label Grid.Row="1"
                                Grid.Column="0"
                                Text="Registration:"
                                FontAttributes="Bold"/>
                        <Label Grid.Row="1"
                                Grid.Column="1"
                                Text="{Binding RegistrationStatus}"/>

                        <Label Grid.Row="2"
                                Grid.Column="0"
                                Text="Token:"
                                FontAttributes="Bold"/>
                        <Label Grid.Row="2"
                                Grid.Column="1"
                               Text="{Binding CurrentToken, StringFormat='{0:...}'}"
                               LineBreakMode="TailTruncation"/>

                        <Label Grid.Row="3"
                                Grid.Column="0"
                                Text="Status:"
                                FontAttributes="Bold"/>
                        <Label Grid.Row="3"
                                Grid.Column="1"
                                Text="{Binding StatusMessage}"/>
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- Action Buttons -->
            <Frame BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                   BorderColor="Transparent"
                   CornerRadius="10"
                   Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="Actions"
                           FontSize="18"
                           FontAttributes="Bold"/>

                    <Grid ColumnDefinitions="*,*"
                            RowDefinitions="Auto,Auto,Auto"
                            ColumnSpacing="10"
                            RowSpacing="10">
                        <Button Grid.Row="0"
                                Grid.Column="0"
                                Text="Initialize"
                                Command="{Binding InitializeCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="0"
                                Grid.Column="1"
                                Text="Register"
                                Command="{Binding RegisterCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="1"
                                Grid.Column="0"
                                Text="Unregister"
                                Command="{Binding UnregisterCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="1"
                                Grid.Column="1"
                                Text="Copy Token"
                                Command="{Binding CopyTokenCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="2"
                                Grid.Column="0"
                                Text="Device Info"
                                Command="{Binding ViewDeviceInfoCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="2"
                                Grid.Column="1"
                                Text="Clear History"
                                Command="{Binding ClearNotificationsCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- Testing and Validation Section -->
            <Frame BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                   BorderColor="Transparent"
                   CornerRadius="10"
                   Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="Testing and Validation"
                           FontSize="18"
                           FontAttributes="Bold"/>

                    <Grid ColumnDefinitions="*,*"
                            RowDefinitions="Auto,Auto"
                            ColumnSpacing="10"
                            RowSpacing="10">
                        <Button Grid.Row="0"
                                Grid.Column="0"
                                Text="Run Test Suite"
                                Command="{Binding RunTestSuiteCommand}"
                                IsEnabled="{Binding IsRunningTests, Converter={toolkit:InvertedBoolConverter}}"
                                BackgroundColor="{StaticResource Secondary}"/>

                        <Button Grid.Row="0"
                                Grid.Column="1"
                                Text="Test Device Support"
                                Command="{Binding TestDeviceSupportCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="1"
                                Grid.Column="0"
                                Text="Test Registration"
                                Command="{Binding TestRegistrationCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>

                        <Button Grid.Row="1"
                                Grid.Column="1"
                                Text="Clear Test History"
                                Command="{Binding ClearTestHistoryCommand}"
                                IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"/>
                    </Grid>

                    <!-- Test Results Display -->
                    <ScrollView MaximumHeightRequest="150"
                            IsVisible="{Binding TestResults, Converter={toolkit:IsStringNotNullOrEmptyConverter}}">
                        <Label Text="{Binding TestResults}"
                               FontFamily="Courier"
                               FontSize="12"
                               BackgroundColor="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}"
                               Padding="10"/>
                    </ScrollView>

                    <!-- Test Progress Indicator -->
                    <ActivityIndicator IsVisible="{Binding IsRunningTests}"
                                       IsRunning="{Binding IsRunningTests}"
                                       Color="{StaticResource Secondary}"/>
                </StackLayout>
            </Frame>

            <!-- Test Notification Section -->
            <Frame BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                   BorderColor="Transparent"
                   CornerRadius="10"
                   Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="Send Test Notification"
                           FontSize="18"
                           FontAttributes="Bold"/>

                    <Entry Placeholder="Notification Title"
                           Text="{Binding TestNotificationTitle}"/>

                    <Entry Placeholder="Notification Body"
                           Text="{Binding TestNotificationBody}"/>

                    <Picker Title="Select Action"
                            ItemsSource="{Binding AvailableActions}"
                            SelectedItem="{Binding TestNotificationAction}"/>

                    <StackLayout Orientation="Horizontal"
                            Spacing="10">
                        <CheckBox IsChecked="{Binding IsSilentNotification}"/>
                        <Label Text="Silent Notification"
                                VerticalOptions="Center"/>
                    </StackLayout>

                    <Button Text="Send Test Notification"
                            Command="{Binding SendTestNotificationCommand}"
                            IsEnabled="{Binding IsBusy, Converter={toolkit:InvertedBoolConverter}}"
                            BackgroundColor="{StaticResource Primary}"/>
                </StackLayout>
            </Frame>

            <!-- Received Notifications -->
            <Frame BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                   BorderColor="Transparent"
                   CornerRadius="10"
                   Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="Received Notifications"
                           FontSize="18"
                           FontAttributes="Bold"/>

                    <CollectionView ItemsSource="{Binding ReceivedNotifications}"
                                    MaximumHeightRequest="300">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:PushNotification">
                                <Frame BackgroundColor="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}"
                                       BorderColor="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray600}}"
                                       CornerRadius="5"
                                       Padding="10"
                                       Margin="0,2">
                                    <StackLayout Spacing="5">
                                        <Grid ColumnDefinitions="*,Auto"
                                                ColumnSpacing="10">
                                            <Label Grid.Column="0"
                                                   Text="{Binding Title}"
                                                   FontAttributes="Bold"
                                                   LineBreakMode="TailTruncation"/>
                                            <Label Grid.Column="1"
                                                   Text="{Binding ReceivedAt, StringFormat='{0:HH:mm:ss}'}"
                                                   FontSize="12"
                                                   TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                                        </Grid>

                                        <Label Text="{Binding Body}"
                                               LineBreakMode="WordWrap"
                                               MaxLines="2"/>

                                        <StackLayout Orientation="Horizontal"
                                                Spacing="10">
                                            <Label Text="{Binding Action, StringFormat='Action: {0}'}"
                                                   FontSize="12"
                                                   TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                                            <Label Text="{Binding IsSilent, StringFormat='Silent: {0}'}"
                                                   FontSize="12"
                                                   TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                                        </StackLayout>
                                    </StackLayout>
                                </Frame>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                        <CollectionView.EmptyView>
                            <Label Text="No notifications received yet"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                        </CollectionView.EmptyView>
                    </CollectionView>
                </StackLayout>
            </Frame>

            <!-- Loading Indicator -->
            <ActivityIndicator IsVisible="{Binding IsBusy}"
                               IsRunning="{Binding IsBusy}"
                               Color="{StaticResource Primary}"
                               VerticalOptions="Center"/>

        </StackLayout>
    </ScrollView>
</ContentPage>
