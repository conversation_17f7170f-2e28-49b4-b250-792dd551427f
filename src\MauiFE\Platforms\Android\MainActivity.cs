﻿using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using Firebase.Messaging;
using MauiApp10PushNotifications.Services;

namespace MauiApp10PushNotifications;

[Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
public class MainActivity : MauiAppCompatActivity, Android.Gms.Tasks.IOnSuccessListener
{
    private INotificationActionService? notificationActionService;
    private IDeviceInstallationService? deviceInstallationService;
    private INotificationRegistrationService? registrationService;

    protected override void OnCreate(Bundle? savedInstanceState)
    {
        base.OnCreate(savedInstanceState);

        // Initialize Firebase and request notification permissions
        InitializeNotifications();

        // Process any notification actions from app launch
        ProcessNotificationActions(Intent);
    }

    protected override void OnNewIntent(Intent? intent)
    {
        base.OnNewIntent(intent);
        ProcessNotificationActions(intent);
    }

    private void InitializeNotifications()
    {
        try
        {
            if (DeviceInstallationService?.NotificationsSupported == true)
            {
                // Request notification permission for Android 13+
                if (Build.VERSION.SdkInt >= BuildVersionCodes.Tiramisu)
                {
                    RequestNotificationPermission();
                }

                // Get Firebase token
                FirebaseMessaging.Instance.GetToken().AddOnSuccessListener(this);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error initializing notifications: {ex.Message}");
        }
    }

    private void RequestNotificationPermission()
    {
        try
        {
            if (ContextCompat.CheckSelfPermission(this, Android.Manifest.Permission.PostNotifications)
                != Permission.Granted)
            {
                ActivityCompat.RequestPermissions(this,
                    new[] { Android.Manifest.Permission.PostNotifications },
                    1001);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error requesting notification permission: {ex.Message}");
        }
    }

    private void ProcessNotificationActions(Intent? intent)
    {
        try
        {
            if (intent?.HasExtra("action") == true)
            {
                var action = intent.GetStringExtra("action");
                if (!string.IsNullOrEmpty(action))
                {
                    NotificationActionService?.TriggerAction(action);
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error processing notification actions: {ex.Message}");
        }
    }

    public override void OnRequestPermissionsResult(int requestCode, string[] permissions, Permission[] grantResults)
    {
        base.OnRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == 1001) // Notification permission request
        {
            if (grantResults.Length > 0 && grantResults[0] == Permission.Granted)
            {
                System.Diagnostics.Debug.WriteLine("Notification permission granted");
                // Re-initialize notifications now that permission is granted
                InitializeNotifications();
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("Notification permission denied");
            }
        }
    }

    // IOnSuccessListener implementation for Firebase token
    public void OnSuccess(Java.Lang.Object result)
    {
        try
        {
            var token = result?.ToString();
            if (!string.IsNullOrEmpty(token))
            {
                System.Diagnostics.Debug.WriteLine($"Firebase token received: {token}");

                if (DeviceInstallationService != null)
                {
                    DeviceInstallationService.Token = token;

                    // Refresh registration with new token
                    Task.Run(async () =>
                    {
                        try
                        {
                            await RegistrationService?.RefreshRegistrationAsync();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error refreshing registration: {ex.Message}");
                        }
                    });
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error handling Firebase token: {ex.Message}");
        }
    }

    // Lazy-loaded service properties
    private INotificationActionService? NotificationActionService =>
        notificationActionService ??= IPlatformApplication.Current?.Services?.GetService<INotificationActionService>();

    private IDeviceInstallationService? DeviceInstallationService =>
        deviceInstallationService ??= IPlatformApplication.Current?.Services?.GetService<IDeviceInstallationService>();

    private INotificationRegistrationService? RegistrationService =>
        registrationService ??= IPlatformApplication.Current?.Services?.GetService<INotificationRegistrationService>();
}
