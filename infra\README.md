# Azure Notification Hubs Infrastructure Deployment

This directory contains Infrastructure as Code (IaC) templates for deploying Azure Notification Hubs for the MAUI Push Notifications application.

## Prerequisites

- Azure CLI installed and configured
- Azure subscription with appropriate permissions
- Resource group created (or permissions to create one)

## Quick Deployment

### Using Azure CLI

```bash
# Login to Azure
az login

# Set your subscription (if you have multiple)
az account set --subscription "your-subscription-id"

# Create resource group (if it doesn't exist)
az group create --name "rg-maui-push-notifications" --location "East US"

# Deploy the notification hub
az deployment group create \
  --resource-group "rg-maui-push-notifications" \
  --template-file notification-hub.bicep \
  --parameters namespaceName="maui-push-ns-$(date +%s)" \
               notificationHubName="maui-push-hub" \
               location="East US" \
               skuTier="Free"
```

### Using PowerShell

```powershell
# Login to Azure
Connect-AzAccount

# Set your subscription (if you have multiple)
Set-AzContext -SubscriptionId "your-subscription-id"

# Create resource group (if it doesn't exist)
New-AzResourceGroup -Name "rg-maui-push-notifications" -Location "East US"

# Deploy the notification hub
New-AzResourceGroupDeployment `
  -ResourceGroupName "rg-maui-push-notifications" `
  -TemplateFile "notification-hub.bicep" `
  -namespaceName "maui-push-ns-$(Get-Date -Format 'yyyyMMddHHmmss')" `
  -notificationHubName "maui-push-hub" `
  -location "East US" `
  -skuTier "Free"
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `namespaceName` | string | Required | The name of the notification hub namespace (must be globally unique) |
| `notificationHubName` | string | Required | The name of the notification hub |
| `location` | string | Resource Group Location | The Azure region for deployment |
| `skuTier` | string | Free | The pricing tier (Free, Basic, Standard) |
| `tags` | object | {} | Tags to apply to all resources |

## Outputs

After successful deployment, the template provides:

- `namespaceResourceId`: Resource ID of the notification hub namespace
- `notificationHubResourceId`: Resource ID of the notification hub
- `namespaceName`: Name of the deployed namespace
- `notificationHubName`: Name of the deployed notification hub
- `connectionString`: Connection string for the application
- `primaryKey`: Primary access key
- `secondaryKey`: Secondary access key

## Post-Deployment Configuration

### 1. Configure Firebase Cloud Messaging (Android)

After deployment, configure FCM in the Azure portal:

1. Navigate to your notification hub in the Azure portal
2. Go to Settings → Google (FCM v1)
3. Enter your Firebase project details:
   - Private Key (from Firebase service account JSON)
   - Client Email (from Firebase service account JSON)
   - Project ID (from Firebase project)

### 2. Configure Apple Push Notification Service (iOS)

Configure APNS in the Azure portal:

1. Navigate to your notification hub in the Azure portal
2. Go to Settings → Apple (APNS)
3. Upload your certificate or configure token-based authentication
4. Set application mode (Sandbox for development, Production for release)

### 3. Update Application Configuration

Update your MAUI application with the deployment outputs:

```csharp
// In MauiProgram.cs
builder.Services.Configure<NotificationHubOptions>(options =>
{
    options.Name = "your-deployed-hub-name";
    options.ConnectionString = "your-connection-string";
});
```

## Environment-Specific Deployments

### Development Environment

```bash
az deployment group create \
  --resource-group "rg-maui-push-dev" \
  --template-file notification-hub.bicep \
  --parameters namespaceName="maui-push-dev-ns" \
               notificationHubName="maui-push-dev-hub" \
               skuTier="Free" \
               tags='{"Environment":"Development","Project":"MAUI-Push"}'
```

### Production Environment

```bash
az deployment group create \
  --resource-group "rg-maui-push-prod" \
  --template-file notification-hub.bicep \
  --parameters namespaceName="maui-push-prod-ns" \
               notificationHubName="maui-push-prod-hub" \
               skuTier="Standard" \
               tags='{"Environment":"Production","Project":"MAUI-Push"}'
```

## Monitoring and Maintenance

### Enable Diagnostic Settings

```bash
# Enable diagnostic logs
az monitor diagnostic-settings create \
  --name "notification-hub-diagnostics" \
  --resource "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.NotificationHubs/namespaces/{namespace-name}/notificationHubs/{hub-name}" \
  --logs '[{"category":"OperationalLogs","enabled":true}]' \
  --metrics '[{"category":"AllMetrics","enabled":true}]' \
  --workspace "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.OperationalInsights/workspaces/{workspace-name}"
```

### Set Up Alerts

Create alerts for failed notifications, high error rates, or quota limits.

## Cost Optimization

- **Free Tier**: Suitable for development and small-scale testing (1M pushes/month)
- **Basic Tier**: For production apps with moderate volume (10M pushes/month)
- **Standard Tier**: For high-volume applications with advanced features

## Security Best Practices

1. **Use Azure Key Vault** for storing connection strings in production
2. **Implement least-privilege access** using Azure RBAC
3. **Rotate access keys regularly**
4. **Monitor access patterns** and set up alerts for unusual activity
5. **Use managed identities** where possible

## Troubleshooting

### Common Deployment Issues

1. **Namespace name conflicts**: Ensure the namespace name is globally unique
2. **Insufficient permissions**: Verify you have Contributor access to the resource group
3. **Region availability**: Some regions may not support all notification hub features

### Validation Commands

```bash
# Verify deployment
az resource show \
  --resource-group "rg-maui-push-notifications" \
  --name "your-hub-name" \
  --resource-type "Microsoft.NotificationHubs/namespaces/notificationHubs"

# Test connectivity
az notification-hub test-send \
  --resource-group "rg-maui-push-notifications" \
  --namespace-name "your-namespace-name" \
  --notification-hub-name "your-hub-name" \
  --platform "fcm" \
  --message "Test notification"
```

## Cleanup

To remove all resources:

```bash
# Delete the entire resource group (WARNING: This deletes ALL resources in the group)
az group delete --name "rg-maui-push-notifications" --yes --no-wait
```

## Support

For issues with:
- **Azure deployment**: Check Azure Activity Log and deployment history
- **Notification delivery**: Review notification hub metrics and logs
- **Application integration**: See the main [Azure setup guide](../docs/AZURE_NOTIFICATION_HUBS_SETUP.md)
