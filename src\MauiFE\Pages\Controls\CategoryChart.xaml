<?xml version="1.0" encoding="utf-8" ?>
<Border xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        xmlns:chart="clr-namespace:Syncfusion.Maui.Toolkit.Charts;assembly=Syncfusion.Maui.Toolkit"
        xmlns:controls="clr-namespace:MauiApp10PushNotifications.Pages.Controls" 
        xmlns:shimmer="clr-namespace:Syncfusion.Maui.Toolkit.Shimmer;assembly=Syncfusion.Maui.Toolkit"
        xmlns:pageModels="clr-namespace:MauiApp10PushNotifications.PageModels"
        x:Class="MauiApp10PushNotifications.Pages.Controls.CategoryChart"
        HeightRequest="{OnIdiom 300, Phone=200}"
        Margin="0, 12"
        Style="{StaticResource CardStyle}"
        x:DataType="pageModels:MainPageModel">
    <shimmer:SfShimmer
        BackgroundColor="Transparent"
        VerticalOptions="FillAndExpand"
        IsActive ="{Binding IsBusy}">
        <shimmer:SfShimmer.CustomView>
            <Grid>
                <BoxView 
                    CornerRadius="12"
                    VerticalOptions="FillAndExpand"
                    Style="{StaticResource ShimmerCustomViewStyle}"/>
            </Grid>
        </shimmer:SfShimmer.CustomView>
        <shimmer:SfShimmer.Content>
            <chart:SfCircularChart x:Name="Chart">
                <chart:SfCircularChart.Legend>
                    <controls:LegendExt Placement="Right">
                        <chart:ChartLegend.LabelStyle>
                            <chart:ChartLegendLabelStyle 
                                TextColor="{AppThemeBinding 
                                Light={StaticResource DarkOnLightBackground},
                                Dark={StaticResource LightOnDarkBackground}}" 
                                Margin="5" 
                                FontSize="18" />
                        </chart:ChartLegend.LabelStyle>
                    </controls:LegendExt>
                </chart:SfCircularChart.Legend>
                <chart:RadialBarSeries 
                    ItemsSource="{Binding TodoCategoryData}"
                    PaletteBrushes="{Binding TodoCategoryColors}"
                    XBindingPath="Title"
                    YBindingPath="Count" 
                    ShowDataLabels="True"
                    EnableTooltip="True" 
                    TrackFill="{AppThemeBinding Light={StaticResource LightBackground}, Dark={StaticResource DarkBackground}}"
                    CapStyle = "BothCurve"/>
            </chart:SfCircularChart>
        </shimmer:SfShimmer.Content>
    </shimmer:SfShimmer>
</Border>
