using CommunityToolkit.Maui;
using MauiApp10PushNotifications.Models;
using Microsoft.Extensions.Logging;
using Syncfusion.Maui.Toolkit.Hosting;

namespace MauiApp10PushNotifications;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureSyncfusionToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                fonts.AddFont("SegoeUI-Semibold.ttf", "SegoeSemibold");
                fonts.AddFont("FluentSystemIcons-Regular.ttf", FluentUI.FontFamily);
            });

#if DEBUG
        builder.Logging.AddDebug();
        builder.Services.AddLogging(configure => configure.AddDebug());
#endif

        // Configure push notification services
        ConfigurePushNotificationServices(builder);

        // Existing services
        builder.Services.AddSingleton<ProjectRepository>();
        builder.Services.AddSingleton<TaskRepository>();
        builder.Services.AddSingleton<CategoryRepository>();
        builder.Services.AddSingleton<TagRepository>();
        builder.Services.AddSingleton<SeedDataService>();
        builder.Services.AddSingleton<ModalErrorHandler>();
        builder.Services.AddSingleton<IErrorHandler>(provider => provider.GetRequiredService<ModalErrorHandler>());

        // Existing page models
        builder.Services.AddSingleton<MainPageModel>();
        builder.Services.AddSingleton<ProjectListPageModel>();
        builder.Services.AddSingleton<ManageMetaPageModel>();

        // Push notification page model
        builder.Services.AddTransient<PushNotificationPageModel>();

        // Existing routes
        builder.Services.AddTransientWithShellRoute<ProjectDetailPage, ProjectDetailPageModel>("project");
        builder.Services.AddTransientWithShellRoute<TaskDetailPage, TaskDetailPageModel>("task");

        // Push notification route
        builder.Services.AddTransientWithShellRoute<PushNotificationPage, PushNotificationPageModel>("pushnotifications");

        return builder.Build();
    }

    private static void ConfigurePushNotificationServices(MauiAppBuilder builder)
    {
        // Configure Azure Notification Hub options
        builder.Services.Configure<NotificationHubOptions>(options =>
        {
            // These should be configured through app settings or environment variables in production
            options.Name = "maui-push-hub"; // TODO: Replace with actual hub name
            options.ConnectionString = "Endpoint=sb://maui-push-notifications-ns.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=a8rD4t596Tp0HAsvQFU3HJjGLBQ3D3P+ZqTH3s8BoFE="; // TODO: Replace with actual connection string
        });

        // Register platform-specific services
#if ANDROID
        builder.Services.AddSingleton<IDeviceInstallationService, MauiApp10PushNotifications.Platforms.Android.DeviceInstallationService>();
#elif IOS
        builder.Services.AddSingleton<IDeviceInstallationService, MauiApp10PushNotifications.Platforms.iOS.DeviceInstallationService>();
#elif MACCATALYST
        builder.Services.AddSingleton<IDeviceInstallationService, MauiApp10PushNotifications.Platforms.MacCatalyst.DeviceInstallationService>();
#elif WINDOWS
        builder.Services.AddSingleton<IDeviceInstallationService, MauiApp10PushNotifications.Platforms.Windows.DeviceInstallationService>();
#endif

        // Register cross-platform services
        builder.Services.AddSingleton<PushNotificationErrorHandler>();
        builder.Services.AddSingleton<NotificationTestingService>();
        builder.Services.AddSingleton<INotificationActionService, NotificationActionService>();
        builder.Services.AddSingleton<INotificationRegistrationService, NotificationRegistrationService>();
        builder.Services.AddSingleton<IPushNotificationService, PushNotificationService>();
    }
}
