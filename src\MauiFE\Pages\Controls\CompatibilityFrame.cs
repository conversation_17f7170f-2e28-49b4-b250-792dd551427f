using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MauiApp10PushNotifications.Pages.Controls;

/// <summary>
/// A custom Frame control that provides backward compatibility with the deprecated Frame control
/// while internally using the new Border control for rendering. This control maintains the same
/// API as the original Frame control to ensure existing XAML code continues to work without modification.
/// </summary>
public class CompatibilityFrame : Border
{
    #region Bindable Properties

    /// <summary>
    /// Bindable property for BackgroundColor to maintain Frame API compatibility
    /// </summary>
    public static readonly BindableProperty BackgroundColorProperty =
        BindableProperty.Create(
            nameof(BackgroundColor),
            typeof(Color),
            typeof(CompatibilityFrame),
            Colors.Transparent,
            propertyChanged: OnBackgroundColorChanged);

    /// <summary>
    /// Bindable property for BorderColor to maintain Frame API compatibility
    /// </summary>
    public static readonly BindableProperty BorderColorProperty =
        BindableProperty.Create(
            nameof(BorderColor),
            typeof(Color),
            typeof(CompatibilityFrame),
            Colors.Transparent,
            propertyChanged: OnBorderColorChanged);

    /// <summary>
    /// Bindable property for CornerRadius to maintain Frame API compatibility
    /// </summary>
    public static readonly BindableProperty CornerRadiusProperty =
        BindableProperty.Create(
            nameof(CornerRadius),
            typeof(float),
            typeof(CompatibilityFrame),
            0f,
            propertyChanged: OnCornerRadiusChanged);

    /// <summary>
    /// Bindable property for HasShadow to maintain Frame API compatibility
    /// Note: Shadow implementation may vary by platform and is mapped to appropriate Border properties
    /// </summary>
    public static readonly BindableProperty HasShadowProperty =
        BindableProperty.Create(
            nameof(HasShadow),
            typeof(bool),
            typeof(CompatibilityFrame),
            false,
            propertyChanged: OnHasShadowChanged);

    /// <summary>
    /// Bindable property for BorderWidth to maintain Frame API compatibility
    /// Maps to Border.StrokeThickness property
    /// </summary>
    public static readonly BindableProperty BorderWidthProperty =
        BindableProperty.Create(
            nameof(BorderWidth),
            typeof(double),
            typeof(CompatibilityFrame),
            0.0,
            propertyChanged: OnBorderWidthChanged);

    #endregion

    #region Properties

    /// <summary>
    /// Gets or sets the background color of the frame.
    /// This property maintains compatibility with the original Frame.BackgroundColor property.
    /// </summary>
    public Color BackgroundColor
    {
        get => (Color)GetValue(BackgroundColorProperty);
        set => SetValue(BackgroundColorProperty, value);
    }

    /// <summary>
    /// Gets or sets the border color of the frame.
    /// This property maintains compatibility with the original Frame.BorderColor property.
    /// </summary>
    public Color BorderColor
    {
        get => (Color)GetValue(BorderColorProperty);
        set => SetValue(BorderColorProperty, value);
    }

    /// <summary>
    /// Gets or sets the corner radius of the frame.
    /// This property maintains compatibility with the original Frame.CornerRadius property.
    /// </summary>
    public float CornerRadius
    {
        get => (float)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    /// <summary>
    /// Gets or sets a value indicating whether the frame should have a shadow.
    /// This property maintains compatibility with the original Frame.HasShadow property.
    /// Note: Shadow rendering may vary by platform.
    /// </summary>
    public bool HasShadow
    {
        get => (bool)GetValue(HasShadowProperty);
        set => SetValue(HasShadowProperty, value);
    }

    /// <summary>
    /// Gets or sets the border width of the frame.
    /// This property maintains compatibility with the original Frame.BorderWidth property.
    /// Maps to Border.StrokeThickness.
    /// </summary>
    public double BorderWidth
    {
        get => (double)GetValue(BorderWidthProperty);
        set => SetValue(BorderWidthProperty, value);
    }

    #endregion

    #region Constructor

    /// <summary>
    /// Initializes a new instance of the CompatibilityFrame class.
    /// Sets up default values to match the original Frame control behavior.
    /// </summary>
    public CompatibilityFrame()
    {
        // Set default values to match original Frame behavior
        this.StrokeThickness = 0; // Frame typically had no visible border by default
        this.Background = new SolidColorBrush(Colors.Transparent);
        this.StrokeShape = new RoundRectangle(0); // Default to no corner radius
    }

    #endregion

    #region Property Changed Handlers

    /// <summary>
    /// Handles changes to the BackgroundColor property and maps it to the Border.Background property.
    /// Supports both direct Color values and theme bindings.
    /// </summary>
    private static void OnBackgroundColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is CompatibilityFrame frame && newValue is Color color)
        {
            // Handle theme binding support - if the color is from a theme binding,
            // the MAUI framework will automatically handle the color changes
            frame.Background = new SolidColorBrush(color);
        }
    }

    /// <summary>
    /// Handles changes to the BorderColor property and maps it to the Border.Stroke property.
    /// </summary>
    private static void OnBorderColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is CompatibilityFrame frame && newValue is Color color)
        {
            frame.Stroke = new SolidColorBrush(color);
            // If a border color is set, ensure there's a visible stroke thickness
            if (color != Colors.Transparent && frame.StrokeThickness == 0)
            {
                frame.StrokeThickness = 1;
            }
        }
    }

    /// <summary>
    /// Handles changes to the CornerRadius property and maps it to the Border.StrokeShape property.
    /// </summary>
    private static void OnCornerRadiusChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is CompatibilityFrame frame && newValue is float radius)
        {
            frame.StrokeShape = new RoundRectangle(radius);
        }
    }

    /// <summary>
    /// Handles changes to the HasShadow property.
    /// Note: Shadow implementation is platform-specific and may require additional platform-specific code.
    /// </summary>
    private static void OnHasShadowChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is CompatibilityFrame frame && newValue is bool hasShadow)
        {
            // Shadow implementation for Border control
            // This is a basic implementation - platform-specific shadow handling may be needed
            if (hasShadow)
            {
                frame.Shadow = new Shadow
                {
                    Brush = new SolidColorBrush(Colors.Black),
                    Opacity = 0.3f,
                    Radius = 4,
                    Offset = new Point(2, 2)
                };
            }
            else
            {
                frame.Shadow = null;
            }
        }
    }

    /// <summary>
    /// Handles changes to the BorderWidth property and maps it to the Border.StrokeThickness property.
    /// </summary>
    private static void OnBorderWidthChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is CompatibilityFrame frame && newValue is double width)
        {
            frame.StrokeThickness = width;
        }
    }

    #endregion
}
