@description('The name of the notification hub namespace')
param namespaceName string

@description('The name of the notification hub')
param notificationHubName string

@description('The location for all resources')
param location string = resourceGroup().location

@description('The SKU tier for the notification hub namespace')
@allowed([
  'Free'
  'Basic'
  'Standard'
])
param skuTier string = 'Free'

@description('Tags to apply to all resources')
param tags object = {}

// Notification Hub Namespace
resource notificationHubNamespace 'Microsoft.NotificationHubs/namespaces@2023-09-01' = {
  name: namespaceName
  location: location
  tags: tags
  sku: {
    name: skuTier
  }
  properties: {
    namespaceType: 'NotificationHub'
  }
}

// Notification Hub
resource notificationHub 'Microsoft.NotificationHubs/namespaces/notificationHubs@2023-09-01' = {
  parent: notificationHubNamespace
  name: notificationHubName
  location: location
  tags: tags
  properties: {
    authorizationRules: [
      {
        name: 'DefaultFullSharedAccessSignature'
        properties: {
          rights: [
            'Listen'
            'Manage'
            'Send'
          ]
        }
      }
    ]
  }
}

// Outputs
@description('The resource ID of the notification hub namespace')
output namespaceResourceId string = notificationHubNamespace.id

@description('The resource ID of the notification hub')
output notificationHubResourceId string = notificationHub.id

@description('The name of the notification hub namespace')
output namespaceName string = notificationHubNamespace.name

@description('The name of the notification hub')
output notificationHubName string = notificationHub.name

@description('The connection string for the notification hub')
output connectionString string = listKeys(resourceId('Microsoft.NotificationHubs/namespaces/notificationHubs/authorizationRules', namespaceName, notificationHubName, 'DefaultFullSharedAccessSignature'), '2023-09-01').primaryConnectionString

@description('The primary key for the notification hub')
output primaryKey string = listKeys(resourceId('Microsoft.NotificationHubs/namespaces/notificationHubs/authorizationRules', namespaceName, notificationHubName, 'DefaultFullSharedAccessSignature'), '2023-09-01').primaryKey

@description('The secondary key for the notification hub')
output secondaryKey string = listKeys(resourceId('Microsoft.NotificationHubs/namespaces/notificationHubs/authorizationRules', namespaceName, notificationHubName, 'DefaultFullSharedAccessSignature'), '2023-09-01').secondaryKey
