# Azure Notification Hubs Setup Guide

This guide provides step-by-step instructions for setting up Azure Notification Hubs to work with your .NET MAUI application for push notifications.

## Prerequisites

- Azure subscription
- Google Firebase account (for Android notifications)
- Apple Developer account (for iOS notifications)
- .NET MAUI development environment

## Part 1: Firebase Cloud Messaging Setup (Android)

### 1.1 Create Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" or "Create a project"
3. Enter project name: `MauiPushNotifications` (or your preferred name)
4. Accept terms and click "Continue"
5. Choose whether to enable Google Analytics (optional)
6. Click "Create project"

### 1.2 Add Android App to Firebase

1. In the Firebase console, click the Android icon to add an Android app
2. Enter the package name: `com.companyname.mauiapp10pushnotifications`
   - This must match the `ApplicationId` in your MAUI project file
3. Enter app nickname (optional): `MAUI Push Notifications`
4. Click "Register app"

### 1.3 Download Configuration File

1. Download the `google-services.json` file
2. Place it in your MAUI project at: `src/MauiFE/Platforms/Android/google-services.json`
3. In Visual Studio, right-click the file and set:
   - Build Action: `GoogleServicesJson`
   - Copy to Output Directory: `Copy always`

### 1.4 Get Firebase Server Key

1. In Firebase Console, go to Project Settings (gear icon)
2. Navigate to "Service accounts" tab
3. Click "Generate new private key"
4. Download the JSON file - you'll need this for Azure Notification Hubs

### 1.5 Enable Firebase Cloud Messaging API

1. In Firebase Console, go to Project Settings
2. Navigate to "Cloud Messaging" tab
3. Ensure "Firebase Cloud Messaging API (V1)" is enabled
4. Note the "Server key" - you'll need this for Azure

## Part 2: Apple Push Notification Service Setup (iOS)

### 2.1 Create App ID

1. Go to [Apple Developer Portal](https://developer.apple.com/account/)
2. Navigate to "Certificates, Identifiers & Profiles"
3. Click "Identifiers" → "App IDs"
4. Click the "+" button to create new App ID
5. Select "App" and click "Continue"
6. Enter:
   - Description: `MAUI Push Notifications`
   - Bundle ID: `com.companyname.mauiapp10pushnotifications`
7. Under "Capabilities", check "Push Notifications"
8. Click "Continue" and "Register"

### 2.2 Create Push Notification Certificate

1. In Apple Developer Portal, go to "Certificates"
2. Click "+" to create new certificate
3. Under "Services", select "Apple Push Notification service SSL (Sandbox & Production)"
4. Select your App ID created in step 2.1
5. Upload a Certificate Signing Request (CSR):
   - Open Keychain Access on Mac
   - Go to Keychain Access → Certificate Assistant → Request Certificate from Certificate Authority
   - Enter your email and name, select "Saved to disk"
   - Upload the generated CSR file
6. Download the certificate and double-click to install in Keychain
7. Export as .p12 file:
   - In Keychain, find your certificate
   - Right-click → Export
   - Choose .p12 format and set a password

## Part 3: Azure Notification Hubs Setup

### 3.1 Create Notification Hub

1. Go to [Azure Portal](https://portal.azure.com)
2. Click "Create a resource"
3. Search for "Notification Hub" and select it
4. Click "Create"
5. Fill in the details:
   - **Subscription**: Your Azure subscription
   - **Resource Group**: Create new or select existing
   - **Namespace Details**: Enter unique namespace name (e.g., `maui-push-notifications-ns`)
   - **Notification Hub Details**: Enter hub name (e.g., `maui-push-hub`)
   - **Location**: Choose your preferred region
6. Click "Review + create" then "Create"

### 3.2 Configure Android (FCM) in Notification Hub

1. Navigate to your created Notification Hub
2. Go to "Settings" → "Google (FCM v1)"
3. Enter the following from your Firebase project:
   - **Private Key**: Copy from the downloaded Firebase service account JSON file (`private_key` field)
   - **Client Email**: Copy from Firebase JSON file (`client_email` field)
   - **Project ID**: Copy from Firebase JSON file (`project_id` field)
4. Click "Save"

### 3.3 Configure iOS (APNS) in Notification Hub

1. In your Notification Hub, go to "Settings" → "Apple (APNS)"
2. Choose authentication mode:
   - **Certificate**: Upload your .p12 file and enter password
   - **Token**: Use if you have Apple Push Notification Authentication Key
3. Set **Application Mode** to "Sandbox" for development
4. Click "Save"

### 3.4 Get Connection String

1. In your Notification Hub, go to "Settings" → "Access Policies"
2. Copy the connection string for `DefaultFullSharedAccessSignature`
3. Note the Notification Hub name from the "Overview" page

## Part 4: Configure Your MAUI Application

### 4.1 Update Configuration

1. Open `src/MauiFE/MauiProgram.cs`
2. Update the `ConfigurePushNotificationServices` method:

```csharp
builder.Services.Configure<NotificationHubOptions>(options =>
{
    options.Name = "your-notification-hub-name"; // Replace with your hub name
    options.ConnectionString = "your-connection-string"; // Replace with your connection string
});
```

### 4.2 Secure Configuration (Production)

For production apps, store sensitive configuration in:

1. **Azure Key Vault** (recommended)
2. **Environment variables**
3. **App settings/configuration files** (not checked into source control)

Example using configuration:

```csharp
builder.Services.Configure<NotificationHubOptions>(
    builder.Configuration.GetSection("NotificationHub"));
```

With `appsettings.json`:

```json
{
  "NotificationHub": {
    "Name": "your-hub-name",
    "ConnectionString": "your-connection-string"
  }
}
```

## Part 5: Testing Your Setup

### 5.1 Test from Azure Portal

1. Go to your Notification Hub in Azure Portal
2. Click "Test Send" in the Overview blade
3. Select platform (Android/iOS)
4. Use test payload:

**Android (FCM v1):**

```json
{
  "message": {
    "notification": {
      "title": "Test Notification",
      "body": "Hello from Azure!"
    },
    "data": {
      "action": "open_app"
    }
  }
}
```

**iOS (APNS):**

```json
{
  "aps": {
    "alert": {
      "title": "Test Notification",
      "body": "Hello from Azure!"
    }
  },
  "action": "open_app"
}
```

### 5.2 Test from Your App

1. Run your MAUI app
2. Navigate to "Push Notifications" page
3. Click "Initialize" then "Register"
4. Use "Send Test Notification" to test end-to-end

## Troubleshooting

### Common Issues

1. **Android notifications not received**
   - Verify `google-services.json` is correctly placed and configured
   - Check Firebase project configuration
   - Ensure notification permissions are granted

2. **iOS notifications not received**
   - Verify certificate is valid and not expired
   - Check bundle ID matches exactly
   - Ensure app is signed with correct provisioning profile

3. **Azure connection issues**
   - Verify connection string is correct
   - Check notification hub name
   - Ensure proper access policies are set

### Debug Steps

1. Check device logs for error messages
2. Verify registration tokens are being generated
3. Test with Azure Portal "Test Send" feature
4. Check Azure Notification Hub metrics and logs

## Security Considerations

1. **Never commit secrets to source control**
2. **Use Azure Key Vault for production secrets**
3. **Rotate certificates and keys regularly**
4. **Use least-privilege access policies**
5. **Monitor notification hub usage and costs**

## Production Deployment

### Infrastructure as Code

Consider using Azure Resource Manager (ARM) templates or Bicep files to automate the deployment of your notification hub infrastructure.

### Monitoring and Analytics

1. Enable Azure Monitor for your Notification Hub
2. Set up alerts for failed notifications
3. Monitor registration and delivery metrics
4. Implement custom telemetry in your app

### Scaling Considerations

1. **Notification Hub Tiers**: Choose appropriate tier based on volume
2. **Rate Limiting**: Implement proper rate limiting for notification sending
3. **Batch Processing**: Use batch APIs for high-volume scenarios
4. **Geographic Distribution**: Consider multiple hubs for global apps

## Next Steps

- Implement custom notification templates
- Add notification analytics and tracking
- Set up automated deployment pipelines
- Configure notification scheduling
- Implement user segmentation and targeting
