using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiApp10PushNotifications.Models;
using MauiApp10PushNotifications.Services;
using System.Collections.ObjectModel;

namespace MauiApp10PushNotifications.PageModels;

public partial class PushNotificationPageModel : ObservableObject
{
    private readonly IPushNotificationService pushNotificationService;
    private readonly ILogger<PushNotificationPageModel> logger;

    [ObservableProperty]
    private bool isRegistered;

    [ObservableProperty]
    private bool isSupported;

    [ObservableProperty]
    private string registrationStatus = "Not initialized";

    [ObservableProperty]
    private string? currentToken;

    [ObservableProperty]
    private string testNotificationTitle = "Test Notification";

    [ObservableProperty]
    private string testNotificationBody = "This is a test notification from the app";

    [ObservableProperty]
    private string testNotificationAction = "open_app";

    [ObservableProperty]
    private bool isSilentNotification;

    [ObservableProperty]
    private bool isBusy;

    [ObservableProperty]
    private string statusMessage = string.Empty;

    [ObservableProperty]
    private bool isRunningTests;

    [ObservableProperty]
    private string testResults = string.Empty;

    public ObservableCollection<PushNotification> ReceivedNotifications { get; } = new();
    public ObservableCollection<NotificationTestResult> TestHistory { get; } = new();
    public ObservableCollection<string> AvailableActions { get; } = new()
    {
        "open_app",
        "view_tasks",
        "view_projects"
    };

    private readonly IErrorHandler errorHandler;
    private readonly NotificationTestingService testingService;

    public PushNotificationPageModel(
        IPushNotificationService pushNotificationService,
        ILogger<PushNotificationPageModel> logger,
        IErrorHandler errorHandler,
        NotificationTestingService testingService)
    {
        this.pushNotificationService = pushNotificationService;
        this.logger = logger;
        this.errorHandler = errorHandler;
        this.testingService = testingService;

        // Subscribe to service events
        this.pushNotificationService.NotificationReceived += OnNotificationReceived;
        this.pushNotificationService.RegistrationStatusChanged += OnRegistrationStatusChanged;
        this.testingService.TestCompleted += OnTestCompleted;
    }

    [RelayCommand]
    private async Task InitializeAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Initializing push notifications...";

            await this.pushNotificationService.InitializeAsync();

            IsSupported = this.pushNotificationService.IsSupported;
            UpdateRegistrationInfo(this.pushNotificationService.RegistrationStatus);

            StatusMessage = IsSupported ? "Push notifications initialized" : "Push notifications not supported";
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error initializing push notifications");
            StatusMessage = $"Error: {ex.Message}";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task RegisterAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Registering for push notifications...";

            var success = await this.pushNotificationService.RegisterAsync("general", "test");

            if (success)
            {
                StatusMessage = "Successfully registered for push notifications";
            }
            else
            {
                StatusMessage = "Failed to register for push notifications";
            }
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error registering for push notifications");
            StatusMessage = $"Registration error: {ex.Message}";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task UnregisterAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Unregistering from push notifications...";

            var success = await this.pushNotificationService.UnregisterAsync();

            if (success)
            {
                StatusMessage = "Successfully unregistered from push notifications";
            }
            else
            {
                StatusMessage = "Failed to unregister from push notifications";
            }
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error unregistering from push notifications");
            StatusMessage = $"Unregistration error: {ex.Message}";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task SendTestNotificationAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Sending test notification...";

            var request = new NotificationRequest
            {
                Text = TestNotificationBody,
                Action = TestNotificationAction,
                Silent = IsSilentNotification,
                Tags = Array.Empty<string>() // Broadcast to all
            };

            var success = await this.pushNotificationService.SendTestNotificationAsync(request);

            if (success)
            {
                StatusMessage = "Test notification sent successfully";
            }
            else
            {
                StatusMessage = "Failed to send test notification";
            }
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error sending test notification");
            StatusMessage = $"Send error: {ex.Message}";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private void ClearNotifications()
    {
        ReceivedNotifications.Clear();
        StatusMessage = "Notification history cleared";
    }

    [RelayCommand]
    private async Task CopyTokenAsync()
    {
        try
        {
            if (!string.IsNullOrEmpty(CurrentToken))
            {
                await Clipboard.SetTextAsync(CurrentToken);
                StatusMessage = "Token copied to clipboard";
            }
            else
            {
                StatusMessage = "No token available to copy";
            }
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error copying token");
            StatusMessage = "Failed to copy token";
            this.errorHandler.HandleError(ex);
        }
    }

    [RelayCommand]
    private void ViewDeviceInfo()
    {
        try
        {
            var deviceInstallation = this.pushNotificationService.GetDeviceInstallation();

            if (deviceInstallation != null)
            {
                var info = $"Device ID: {deviceInstallation.InstallationId}\n" +
                          $"Platform: {deviceInstallation.Platform}\n" +
                          $"Token: {deviceInstallation.PushChannel}\n" +
                          $"Tags: {string.Join(", ", deviceInstallation.Tags)}";

                StatusMessage = "Device info logged to debug output";
                this.logger.LogInformation("Device Installation Info:\n{Info}", info);
            }
            else
            {
                StatusMessage = "No device installation info available";
            }
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error getting device info");
            StatusMessage = "Failed to get device info";
            this.errorHandler.HandleError(ex);
        }
    }

    private void OnNotificationReceived(object? sender, PushNotification notification)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            ReceivedNotifications.Insert(0, notification);
            StatusMessage = $"Notification received: {notification.Title}";

            // Keep only the last 50 notifications
            while (ReceivedNotifications.Count > 50)
            {
                ReceivedNotifications.RemoveAt(ReceivedNotifications.Count - 1);
            }
        });
    }

    private void OnRegistrationStatusChanged(object? sender, NotificationRegistrationStatus status)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            UpdateRegistrationInfo(status);
        });
    }

    private void UpdateRegistrationInfo(NotificationRegistrationStatus status)
    {
        IsRegistered = status.IsRegistered;
        CurrentToken = status.Token;

        if (status.IsRegistered)
        {
            RegistrationStatus = $"Registered ({status.LastUpdated:HH:mm:ss})";
        }
        else if (!string.IsNullOrEmpty(status.Error))
        {
            RegistrationStatus = $"Error: {status.Error}";
        }
        else
        {
            RegistrationStatus = "Not registered";
        }
    }

    [RelayCommand]
    private async Task RunTestSuiteAsync()
    {
        try
        {
            IsRunningTests = true;
            StatusMessage = "Running comprehensive test suite...";
            TestResults = "Running tests...";

            var suite = await this.testingService.RunTestSuiteAsync();

            var results = $"Test Suite Results:\n" +
                         $"Total Tests: {suite.TotalTests}\n" +
                         $"Passed: {suite.PassedTests}\n" +
                         $"Failed: {suite.FailedTests}\n" +
                         $"Duration: {suite.Duration.TotalSeconds:F1}s\n" +
                         $"Overall: {(suite.AllTestsPassed ? "PASSED" : "FAILED")}\n\n";

            foreach (var test in suite.Results)
            {
                results += $"• {test.TestName}: {(test.Passed ? "PASS" : "FAIL")}\n";
                if (!test.Passed)
                {
                    results += $"  Error: {test.Message}\n";
                }
            }

            TestResults = results;
            StatusMessage = suite.AllTestsPassed
                ? "All tests passed successfully!"
                : $"Test suite completed with {suite.FailedTests} failures";
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error running test suite");
            TestResults = $"Test suite failed: {ex.Message}";
            StatusMessage = "Test suite failed";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsRunningTests = false;
        }
    }

    [RelayCommand]
    private async Task TestDeviceSupportAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Testing device support...";

            var result = await this.testingService.TestDeviceSupportAsync();
            StatusMessage = result.Message;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error testing device support");
            StatusMessage = "Device support test failed";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private async Task TestRegistrationAsync()
    {
        try
        {
            IsBusy = true;
            StatusMessage = "Testing registration...";

            var result = await this.testingService.TestDeviceRegistrationAsync();
            StatusMessage = result.Message;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Error testing registration");
            StatusMessage = "Registration test failed";
            this.errorHandler.HandleError(ex);
        }
        finally
        {
            IsBusy = false;
        }
    }

    [RelayCommand]
    private void ClearTestHistory()
    {
        TestHistory.Clear();
        TestResults = string.Empty;
        StatusMessage = "Test history cleared";
    }

    private void OnTestCompleted(object? sender, NotificationTestResult result)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            TestHistory.Insert(0, result);

            // Keep only the last 20 test results
            while (TestHistory.Count > 20)
            {
                TestHistory.RemoveAt(TestHistory.Count - 1);
            }
        });
    }
}
