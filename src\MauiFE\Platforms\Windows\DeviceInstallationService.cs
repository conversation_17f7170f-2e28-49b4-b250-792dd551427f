using MauiApp10PushNotifications.Models;
using MauiApp10PushNotifications.Services;
using System.Security.Cryptography;
using System.Text;

namespace MauiApp10PushNotifications.Platforms.Windows;

/// <summary>
/// Windows-specific device installation service
/// Uses WNS (Windows Notification Service) for push notifications
/// </summary>
public class DeviceInstallationService : IDeviceInstallationService
{
    private const int SupportedVersionMajor = 10;
    private const int SupportedVersionMinor = 0;
    private const int SupportedBuild = 17763; // Windows 10 version 1809

    /// <summary>
    /// Gets or sets the push notification token
    /// </summary>
    public string? Token { get; set; }

    /// <summary>
    /// Checks if notifications are supported on this device
    /// Windows 10 version 1809 and above support modern notifications
    /// </summary>
    public bool NotificationsSupported
    {
        get
        {
            try
            {
                var version = Environment.OSVersion.Version;
                return version.Major >= SupportedVersionMajor && 
                       version.Build >= SupportedBuild;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Gets a unique device identifier for Windows
    /// </summary>
    public string GetDeviceId()
    {
        try
        {
            // Use machine name and user name to create a consistent device ID
            var machineInfo = $"{Environment.MachineName}_{Environment.UserName}";
            
            // Create a hash to ensure consistent length and format
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo));
            return Convert.ToHexString(hashBytes)[..32]; // Take first 32 characters
        }
        catch
        {
            // Fallback to a new GUID if anything fails
            return Guid.NewGuid().ToString("N")[..32];
        }
    }

    /// <summary>
    /// Gets the device installation information for Windows
    /// </summary>
    public DeviceInstallation GetDeviceInstallation(params string[] tags)
    {
        if (!NotificationsSupported)
        {
            throw new Exception(GetNotificationsSupportError());
        }

        if (string.IsNullOrWhiteSpace(Token))
        {
            throw new Exception("Unable to resolve token for WNS");
        }

        var installation = new DeviceInstallation
        {
            InstallationId = GetDeviceId(),
            Platform = "wns", // Windows Notification Service
            PushChannel = Token
        };

        if (tags?.Length > 0)
        {
            foreach (var tag in tags)
            {
                installation.Tags.Add(tag);
            }
        }

        return installation;
    }

    private string GetNotificationsSupportError()
    {
        if (!NotificationsSupported)
        {
            var version = Environment.OSVersion.Version;
            return $"This app only supports notifications on Windows {SupportedVersionMajor}.{SupportedVersionMinor} build {SupportedBuild} and above. You are running Windows {version.Major}.{version.Minor} build {version.Build}.";
        }

        if (string.IsNullOrWhiteSpace(Token))
        {
            return "This app can support notifications but you must enable this in your settings.";
        }

        return "An error occurred preventing the use of push notifications";
    }
}
