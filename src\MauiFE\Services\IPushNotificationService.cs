using MauiApp10PushNotifications.Models;

namespace MauiApp10PushNotifications.Services;

/// <summary>
/// Service for managing push notification registration and handling
/// </summary>
public interface IPushNotificationService
{
    /// <summary>
    /// Event fired when a push notification is received
    /// </summary>
    event EventHandler<PushNotification>? NotificationReceived;

    /// <summary>
    /// Event fired when the registration status changes
    /// </summary>
    event EventHandler<NotificationRegistrationStatus>? RegistrationStatusChanged;

    /// <summary>
    /// Gets the current registration status
    /// </summary>
    NotificationRegistrationStatus RegistrationStatus { get; }

    /// <summary>
    /// Initializes the push notification service
    /// </summary>
    Task InitializeAsync();

    /// <summary>
    /// Registers the device for push notifications
    /// </summary>
    /// <param name="tags">Optional tags for targeting specific notifications</param>
    Task<bool> RegisterAsync(params string[] tags);

    /// <summary>
    /// Unregisters the device from push notifications
    /// </summary>
    Task<bool> UnregisterAsync();

    /// <summary>
    /// Sends a test notification (for testing purposes)
    /// </summary>
    /// <param name="request">The notification request</param>
    Task<bool> SendTestNotificationAsync(NotificationRequest request);

    /// <summary>
    /// Gets the device installation information
    /// </summary>
    DeviceInstallation? GetDeviceInstallation(params string[] tags);

    /// <summary>
    /// Checks if push notifications are supported on this device
    /// </summary>
    bool IsSupported { get; }

    /// <summary>
    /// Handles received push notifications
    /// </summary>
    /// <param name="notification">The received notification</param>
    void HandleReceivedNotification(PushNotification notification);
}

/// <summary>
/// Platform-specific device installation service
/// </summary>
public interface IDeviceInstallationService
{
    /// <summary>
    /// Gets or sets the push notification token
    /// </summary>
    string? Token { get; set; }

    /// <summary>
    /// Checks if notifications are supported on this device
    /// </summary>
    bool NotificationsSupported { get; }

    /// <summary>
    /// Gets a unique device identifier
    /// </summary>
    string GetDeviceId();

    /// <summary>
    /// Gets the device installation information
    /// </summary>
    /// <param name="tags">Optional tags for targeting</param>
    DeviceInstallation GetDeviceInstallation(params string[] tags);
}

/// <summary>
/// Service for handling notification actions
/// </summary>
public interface INotificationActionService
{
    /// <summary>
    /// Event fired when a notification action is triggered
    /// </summary>
    event EventHandler<string>? ActionTriggered;

    /// <summary>
    /// Triggers a notification action
    /// </summary>
    /// <param name="action">The action to trigger</param>
    void TriggerAction(string action);
}

/// <summary>
/// Service for managing notification hub registration
/// </summary>
public interface INotificationRegistrationService
{
    /// <summary>
    /// Refreshes the device registration with the notification hub
    /// </summary>
    Task<bool> RefreshRegistrationAsync();

    /// <summary>
    /// Deregisters the device from the notification hub
    /// </summary>
    Task<bool> DeregisterDeviceAsync();
}
