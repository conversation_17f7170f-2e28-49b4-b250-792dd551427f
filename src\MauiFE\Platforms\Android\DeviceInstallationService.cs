using Android;
using Android.Content;
using AndroidX.Core.Content;
using MauiApp10PushNotifications.Models;
using MauiApp10PushNotifications.Services;

namespace MauiApp10PushNotifications.Platforms.Android;

/// <summary>
/// Android-specific device installation service
/// </summary>
public class DeviceInstallationService : IDeviceInstallationService
{
    private const int SupportedVersionMajor = 8;
    private const int SupportedVersionMinor = 0;

    /// <summary>
    /// Gets or sets the push notification token
    /// </summary>
    public string? Token { get; set; }

    /// <summary>
    /// Checks if notifications are supported on this device
    /// </summary>
    public bool NotificationsSupported
    {
        get
        {
            try
            {
                var context = Platform.CurrentActivity?.ApplicationContext ?? global::Android.App.Application.Context;

                // Check Android version
                if (global::Android.OS.Build.VERSION.SdkInt < global::Android.OS.BuildVersionCodes.O)
                {
                    return false;
                }

                // Check if POST_NOTIFICATIONS permission is granted (Android 13+)
                if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.Tiramisu)
                {
                    var permission = ContextCompat.CheckSelfPermission(context, Manifest.Permission.PostNotifications);
                    return permission == global::Android.Content.PM.Permission.Granted;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Gets a unique device identifier
    /// </summary>
    public string GetDeviceId()
    {
        try
        {
            var context = Platform.CurrentActivity?.ApplicationContext ?? global::Android.App.Application.Context;

            // Use Android ID as device identifier
            var androidId = global::Android.Provider.Settings.Secure.GetString(
                context.ContentResolver,
                global::Android.Provider.Settings.Secure.AndroidId);

            return androidId ?? Guid.NewGuid().ToString();
        }
        catch
        {
            return Guid.NewGuid().ToString();
        }
    }

    /// <summary>
    /// Gets the device installation information
    /// </summary>
    public DeviceInstallation GetDeviceInstallation(params string[] tags)
    {
        if (!NotificationsSupported)
        {
            throw new Exception(GetNotificationsSupportError());
        }

        if (string.IsNullOrWhiteSpace(Token))
        {
            throw new Exception("Unable to resolve token for FCM");
        }

        var installation = new DeviceInstallation
        {
            InstallationId = GetDeviceId(),
            Platform = "fcmv1",
            PushChannel = Token
        };

        if (tags?.Length > 0)
        {
            foreach (var tag in tags)
            {
                installation.Tags.Add(tag);
            }
        }

        return installation;
    }

    private string GetNotificationsSupportError()
    {
        if (!NotificationsSupported)
        {
            if (global::Android.OS.Build.VERSION.SdkInt < global::Android.OS.BuildVersionCodes.O)
            {
                return $"This app only supports notifications on Android {SupportedVersionMajor}.{SupportedVersionMinor} and above.";
            }

            if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.Tiramisu)
            {
                return "This app requires notification permission. Please enable notifications in app settings.";
            }
        }

        if (string.IsNullOrWhiteSpace(Token))
        {
            return "This app can support notifications but you must enable this in your settings.";
        }

        return "An error occurred preventing the use of push notifications";
    }
}
