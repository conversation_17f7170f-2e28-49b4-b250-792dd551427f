﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
	<application android:allowBackup="true" android:icon="@mipmap/appicon" android:roundIcon="@mipmap/appicon_round" android:supportsRtl="true"></application>

	<!-- Network permissions -->
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />

	<!-- Push notification permissions -->
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

	<!-- Firebase Cloud Messaging permissions -->
	<permission android:name="com.companyname.mauiapp10pushnotifications.permission.C2D_MESSAGE" android:protectionLevel="signature" />
	<uses-permission android:name="com.companyname.mauiapp10pushnotifications.permission.C2D_MESSAGE" />
</manifest>