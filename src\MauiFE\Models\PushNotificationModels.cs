using System.ComponentModel.DataAnnotations;

namespace MauiApp10PushNotifications.Models;

/// <summary>
/// Represents a device installation for push notifications
/// </summary>
public class DeviceInstallation
{
    [Required]
    public string InstallationId { get; set; } = string.Empty;

    [Required]
    public string Platform { get; set; } = string.Empty;

    [Required]
    public string PushChannel { get; set; } = string.Empty;

    public IList<string> Tags { get; set; } = new List<string>();
}

/// <summary>
/// Represents a push notification request
/// </summary>
public class NotificationRequest
{
    public string Text { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string[] Tags { get; set; } = Array.Empty<string>();
    public bool Silent { get; set; }
}

/// <summary>
/// Represents a received push notification
/// </summary>
public class PushNotification
{
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, string> Data { get; set; } = new();
    public DateTime ReceivedAt { get; set; } = DateTime.Now;
    public bool IsSilent { get; set; }
}

/// <summary>
/// Represents the registration status of push notifications
/// </summary>
public class NotificationRegistrationStatus
{
    public bool IsRegistered { get; set; }
    public string? Token { get; set; }
    public string? Error { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.Now;
}

/// <summary>
/// Configuration options for Azure Notification Hubs
/// </summary>
public class NotificationHubOptions
{
    [Required]
    public string Name { get; set; } = string.Empty;

    [Required]
    public string ConnectionString { get; set; } = string.Empty;
}

/// <summary>
/// Push notification templates for different platforms
/// </summary>
public static class PushTemplates
{
    public static class Generic
    {
        public const string Android = "{ \"message\" : { \"notification\" : { \"title\" : \"$(title)\", \"body\" : \"$(body)\"}, \"data\" : { \"action\" : \"$(action)\" } } }";
        public const string iOS = "{ \"aps\" : {\"alert\" : { \"title\" : \"$(title)\", \"body\" : \"$(body)\" }}, \"action\" : \"$(action)\" }";
    }

    public static class Silent
    {
        public const string Android = "{ \"message\" : { \"data\" : {\"title\" : \"$(title)\", \"body\" : \"$(body)\", \"action\" : \"$(action)\"} } }";
        public const string iOS = "{ \"aps\" : {\"content-available\" : 1, \"apns-priority\": 5, \"sound\" : \"\", \"badge\" : 0}, \"title\" : \"$(title)\", \"body\" : \"$(body)\", \"action\" : \"$(action)\" }";
    }
}

/// <summary>
/// Represents the result of a notification test
/// </summary>
public class NotificationTestResult
{
    public string TestName { get; set; } = string.Empty;
    public bool Passed { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Represents a complete test suite run
/// </summary>
public class NotificationTestSuite
{
    public string TestName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public List<NotificationTestResult> Results { get; set; } = new();
    public int TotalTests { get; set; }
    public int PassedTests { get; set; }
    public int FailedTests { get; set; }
    public bool AllTestsPassed => FailedTests == 0 && TotalTests > 0;
}
